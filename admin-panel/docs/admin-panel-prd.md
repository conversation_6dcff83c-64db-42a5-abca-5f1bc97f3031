# AuraESIM 后台管理面板 PRD

## 目录

- [项目概述](#%E9%A1%B9%E7%9B%AE%E6%A6%82%E8%BF%B0)
- [设计原则](#%E8%AE%BE%E8%AE%A1%E5%8E%9F%E5%88%99)
- [用户角色与权限](#%E7%94%A8%E6%88%B7%E8%A7%92%E8%89%B2%E4%B8%8E%E6%9D%83%E9%99%90)
- [功能架构](#%E5%8A%9F%E8%83%BD%E6%9E%B6%E6%9E%84)
- [页面结构设计](#%E9%A1%B5%E9%9D%A2%E7%BB%93%E6%9E%84%E8%AE%BE%E8%AE%A1)
- [核心功能模块](#%E6%A0%B8%E5%BF%83%E5%8A%9F%E8%83%BD%E6%A8%A1%E5%9D%97)
- [组件规范](#%E7%BB%84%E4%BB%B6%E8%A7%84%E8%8C%83)
- [响应式设计](#%E5%93%8D%E5%BA%94%E5%BC%8F%E8%AE%BE%E8%AE%A1)
- [技术规范](#%E6%8A%80%E6%9C%AF%E8%A7%84%E8%8C%83)

## 项目概述

AuraESIM后台管理面板是一个基于Web的响应式管理系统，为代理商、企业代理商和平台管理员提供全面的eSIM业务管理功能。系统采用现代化的设计理念，基于shadcn/ui组件库构建，确保优秀的用户体验和高效的业务操作。

### 核心目标

- 为不同角色提供专业化、个性化的管理界面
- 实现高效的eSIM业务运营和管理
- 提供实时的数据统计和业务洞察
- 确保系统的安全性和稳定性

## 设计原则

### 1\. 用户体验优先

- **直观易用**：界面布局清晰，操作流程简单直观
- **响应式设计**：适配桌面、平板、手机等不同设备
- **一致性**：统一的设计语言和交互模式

### 2\. 效率导向

- **快速导航**：清晰的层级结构和便捷的导航方式
- **批量操作**：支持批量处理，提高操作效率
- **智能搜索**：全局搜索和模块内精确搜索

### 3\. 数据驱动

- **实时展示**：关键指标实时更新
- **可视化**：图表展示趋势和统计信息
- **深度分析**：多维度数据分析和报表

### 4\. 安全可靠

- **权限控制**：基于角色的精细化权限管理
- **操作审计**：重要操作记录和审计追踪
- **数据保护**：敏感数据加密和安全传输

## 用户角色与权限

### 代理商 (Reseller)

**核心职责**：管理下属用户和eSIM分配，发展业务 **主要权限**：

- 用户管理：创建、查看、管理下属用户
- 套餐管理：支持多Provider的数据套餐的展示、搜索、筛选和详情查看
- eSIM管理：分配、回收、续费，以及状态管理
- 财务管理：余额查看、充值记录、佣金查看
- 促销管理：创建和管理促销活动
- 费率管理：设置自定义销售费率

### 企业代理商 (Enterprise)

**核心职责**：管理企业内部组织架构和eSIM资源分配 **主要权限**：

- 继承代理商全部权限
- 组织管理：部门创建、员工管理（用户管理）
- 批量操作：批量分配eSIM、批量用户操作
- 企业报表：部门统计、员工使用分析

### 平台管理员 (Admin)

**核心职责**：全平台管理和监控 **主要权限**：

- 全局用户管理：所有用户的查看和管理
- 代理商管理：代理商审核、状态管理、佣金设置
- 系统管理：全局费率、促销活动、系统配置
- 财务管理：平台财务统计、交易记录
- 监控运维：系统监控、日志管理

## 功能架构

```
后台管理面板
├── 认证系统
│   ├── 登录/登出
│   ├── 权限验证
│   └── 会话管理
├── 仪表板
│   ├── 概览统计
│   ├── 实时数据
│   └── 快捷操作
├── 业务管理
│   ├── 套餐管理
│   ├── 用户管理
│   ├── eSIM管理
│   ├── 订单管理
│   └── 促销管理
├── 财务管理
│   ├── 余额管理
│   ├── 交易记录
│   └── 财务报表
├── 系统管理
│   ├── 费率管理
│   ├── 代理商管理
│   └── 系统配置
└── 个人中心
    ├── 个人资料
    ├── 安全设置
    └── 操作日志
```

## 页面结构设计

### 整体布局

采用经典的后台管理系统布局：

```
┌─────────────────────────────────────────────┐
│ Header (顶部导航栏)                           │
├─────────────┬───────────────────────────────┤
│             │                               │
│  Sidebar    │                               │
│  (侧边栏)   │        Main Content           │
│             │        (主内容区)              │
│             │                               │
│             │                               │
├─────────────┴───────────────────────────────┤
│ Footer (页脚) - 可选                         │
└─────────────────────────────────────────────┘
```

### Header (顶部导航栏)

- **左侧**：Logo + 产品名称
- **中间**：面包屑导航
- **右侧**：
  - 全局搜索框
  - 通知中心 (铃铛图标 + 红点提示)
  - 用户头像下拉菜单
  - 主题切换 (明/暗主题)

### Sidebar (侧边栏)

- **可折叠设计**：支持展开/收起
- **分组导航**：按功能模块分组
- **图标 + 文字**：直观的导航标识
- **状态指示**：当前页面高亮显示

### Main Content (主内容区)

- **页面标题**：包含页面名称和关键操作按钮
- **工具栏**：搜索、筛选、导出等功能
- **内容区域**：表格、卡片、表单等内容展示
- **分页组件**：数据分页展示

## 核心功能模块

### 1\. 仪表板 (Dashboard)

#### 1.1 概览统计卡片

**设计样式**：4列网格布局，响应式调整

```
┌──────────┬──────────┬──────────┬──────────┐
│  总用户   │  活跃用户 │  总收入   │  本月订单 │
│  1,234   │   856    │ ¥12,345  │   89     │
│  +12%    │  +8%     │  +15%    │  +23%    │
└──────────┴──────────┴──────────┴──────────┘
```

**卡片组件结构**：

- 主要数字 (大字体)
- 描述文字 (中等字体)
- 变化趋势 (小字体 + 颜色指示)
- 可选图标

#### 1.2 数据可视化图表

**订单趋势图**：

- 类型：折线图
- 时间范围：最近30天/90天/1年
- 数据：订单数量、收入金额

**eSIM状态分布**：

- 类型：环形图
- 数据：活跃、已过期、已暂停等状态占比

**地区分布图**：

- 类型：柱状图
- 数据：各地区eSIM销售情况

#### 1.3 最近活动

- 最新订单列表
- 待处理事项
- 系统通知

### 2\. 用户管理

#### 2.1 用户列表页

**布局结构**：

```
页面标题: 用户管理
┌─ 工具栏 ─────────────────────────────────────┐
│ [搜索框] [状态筛选] [角色筛选] [时间筛选] [新建用户] │
└─────────────────────────────────────────────┘
┌─ 数据表格 ───────────────────────────────────┐
│ ☑ | 头像 | 姓名 | 邮箱 | 角色 | 状态 | 注册时间 | 操作 │
│ ☑ | [头像] | 张三 | <EMAIL> | 用户 | 活跃 | 2024-01-01 | [查看][编辑] │
│ ☑ | [头像] | 李四 | <EMAIL> | 代理商 | 活跃 | 2024-01-02 | [查看][编辑] │
└─────────────────────────────────────────────┘
┌─ 分页组件 ───────────────────────────────────┐
│ 显示 1-10 条，共 100 条        [上一页] 1 2 3 [下一页] │
└─────────────────────────────────────────────┘
```

**表格功能**：

- 全选/单选复选框
- 排序功能 (点击表头)
- 行内操作 (查看、编辑、删除)
- 批量操作 (批量删除、批量状态变更)

#### 2.2 用户详情页

**Tab页结构**：

- 基本信息：个人资料、联系方式
- eSIM记录：已分配的eSIM列表
- 订单记录：历史订单
- 积分记录：积分变更历史
- 操作日志：用户操作记录

### 3\. eSIM管理

#### 3.1 eSIM列表页

**多视图展示**：

- 表格视图 (默认)
- 卡片视图
- 状态看板视图

**卡片视图示例**：

```
┌──────────────────────────────────────┐
│ 📶 欧洲30天5GB套餐                    │
│ ICCID: 8988228801234567890          │
│ 状态: 🟢 已激活  用户: 张三            │
│ 流量: 2.3GB/5GB  有效期: 15天         │
│ ────────────────────────────────────  │
│ [查看详情] [暂停] [续费]               │
└──────────────────────────────────────┘
```

#### 3.2 eSIM详情页

**信息卡片布局**：

- eSIM基本信息
- 激活二维码 (可下载)
- 数据使用记录
- 操作历史记录

### 4\. 订单管理

#### 4.1 订单列表

**时间轴筛选**：

- 今天、昨天、最近7天、最近30天、自定义时间

**状态标签**：

- 待支付 (黄色)
- 处理中 (蓝色)
- 已完成 (绿色)
- 已取消 (灰色)
- 已退款 (红色)

#### 4.2 订单详情

**步骤指示器**：

```
创建订单 → 支付确认 → 处理中 → 完成
   ✓         ✓        ⏳      ○
```

### 5\. 财务管理

#### 5.1 余额概览

**多维度展示**：

- 当前余额
- 冻结金额
- 可用余额
- 本月收入/支出

#### 5.2 交易记录

**交易类型分类**：

- 充值 (绿色 ↗)
- 支出 (红色 ↘)
- 佣金 (蓝色 ★)
- 退款 (橙色 ↩)

### 6\. 促销管理

#### 6.1 促销活动列表

**活动状态**：

- 未开始 (灰色)
- 进行中 (绿色)
- 已结束 (黄色)
- 已停用 (红色)

#### 6.2 创建促销活动

**表单结构**：

- 基本信息：名称、描述、促销码
- 折扣设置：类型(百分比/固定金额)、折扣值
- 限制条件：最小订单金额、最大折扣、使用次数
- 时间设置：开始时间、结束时间
- 适用范围：全局/特定用户群/特定商品

## 组件规范

### 基础组件 (基于 shadcn/ui)

#### 1\. Button 组件

```typescript
// 主要按钮类型
Primary Button: bg-primary text-primary-foreground
Secondary Button: bg-secondary text-secondary-foreground
Destructive Button: bg-destructive text-destructive-foreground
Outline Button: border border-input
Ghost Button: hover:bg-accent hover:text-accent-foreground

// 尺寸规格
Small: h-8 px-3 text-xs
Default: h-9 px-4 py-2 text-sm
Large: h-10 px-8 text-base
```

#### 2\. Card 组件

```typescript
// 基础卡片
Card: rounded-lg border bg-card text-card-foreground shadow-sm

// 卡片头部
CardHeader: p-6 pb-2

// 卡片内容
CardContent: p-6 pt-0

// 卡片底部
CardFooter: flex items-center p-6 pt-0
```

#### 3\. Table 组件

```typescript
// 表格容器
Table: w-full caption-bottom text-sm

// 表格头部
TableHeader: [&_tr]:border-b

// 表格行
TableRow: border-b transition-colors hover:bg-muted/50

// 表格单元格
TableCell: p-2 align-middle [&:has([role=checkbox])]:pr-0
```

### 业务组件

#### 1\. StatCard (统计卡片)

```tsx
interface StatCardProps {
  title: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease'
  }
  icon?: React.ReactNode
  description?: string
}
```

#### 2\. DataTable (数据表格)

```tsx
interface DataTableProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  searchKey?: string
  filterOptions?: FilterOption[]
  enableSelection?: boolean
  onSelectionChange?: (selected: T[]) => void
}
```

#### 3\. FilterBar (筛选栏)

```tsx
interface FilterBarProps {
  filters: FilterConfig[]
  onFilterChange: (filters: Record<string, any>) => void
  searchPlaceholder?: string
  onSearch?: (query: string) => void
}
```

#### 4\. StatusBadge (状态标签)

```tsx
interface StatusBadgeProps {
  status: 'active' | 'inactive' | 'pending' | 'expired'
  children: React.ReactNode
}
```

### 布局组件

#### 1\. PageHeader (页面头部)

```tsx
interface PageHeaderProps {
  title: string
  description?: string
  children?: React.ReactNode // 操作按钮
}
```

#### 2\. DashboardLayout (仪表板布局)

```tsx
interface DashboardLayoutProps {
  children: React.ReactNode
  sidebar: React.ReactNode
  header: React.ReactNode
}
```

#### 3\. ContentContainer (内容容器)

```tsx
interface ContentContainerProps {
  children: React.ReactNode
  className?: string
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
}
```

## 响应式设计

### 断点规范

```css
/* Tailwind CSS 断点 */
sm: 640px   /* 平板竖屏 */
md: 768px   /* 平板横屏 */
lg: 1024px  /* 笔记本 */
xl: 1280px  /* 桌面 */
2xl: 1536px /* 大屏 */
```

### 布局适配

#### 桌面端 (lg+)

- 侧边栏: 固定宽度 256px
- 主内容区: 自适应剩余宽度
- 表格: 完整显示所有列
- 卡片: 4列网格布局

#### 平板端 (md-lg)

- 侧边栏: 可收起至图标模式
- 主内容区: 占满宽度
- 表格: 隐藏次要列
- 卡片: 2-3列网格布局

#### 手机端 (sm-)

- 侧边栏: 抽屉模式
- 主内容区: 全宽度
- 表格: 卡片模式展示
- 卡片: 1列堆叠布局

### 组件响应式示例

#### 统计卡片

```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
  <StatCard />
  <StatCard />
  <StatCard />
  <StatCard />
</div>
```

#### 操作按钮组

```tsx
<div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
  <Button className="w-full sm:w-auto">主要操作</Button>
  <Button variant="outline" className="w-full sm:w-auto">次要操作</Button>
</div>
```

## 技术规范

### 技术栈

- **框架**: Next.js 14 (App Router)
- **UI库**: shadcn/ui + Tailwind CSS
- **状态管理**: Zustand
- **数据获取**: TanStack Query (React Query)
- **表单处理**: React Hook Form + Zod
- **图表库**: Recharts
- **图标**: Lucide React

### 文件组织

```
admin-panel/
├── src/
│   ├── app/                 # App Router 页面
│   ├── components/          # 组件库
│   │   ├── ui/             # shadcn/ui 基础组件
│   │   ├── layout/         # 布局组件
│   │   ├── business/       # 业务组件
│   │   └── charts/         # 图表组件
│   ├── lib/                # 工具函数
│   ├── hooks/              # 自定义 Hooks
│   ├── store/              # 状态管理
│   ├── types/              # TypeScript 类型定义
│   └── styles/             # 样式文件
├── public/                 # 静态资源
└── docs/                   # 文档
```

### 代码规范

#### 组件命名

- 使用 PascalCase: `UserTable`, `StatCard`
- 组件文件: `UserTable.tsx`
- 样式文件: `UserTable.module.css` (如需要)

#### API 接口

- 使用 React Query 进行数据获取
- 统一的错误处理
- 类型安全的 API 调用

```tsx
// 示例: 用户列表查询
export const useUsers = (params: UserQueryParams) => {
  return useQuery({
    queryKey: ['users', params],
    queryFn: () => userAPI.getUsers(params),
    staleTime: 5 * 60 * 1000, // 5分钟
  })
}
```

#### 状态管理

```tsx
// 用户状态管理
interface UserStore {
  users: User[]
  currentUser: User | null
  setUsers: (users: User[]) => void
  setCurrentUser: (user: User) => void
}

export const useUserStore = create<UserStore>((set) => ({
  users: [],
  currentUser: null,
  setUsers: (users) => set({ users }),
  setCurrentUser: (currentUser) => set({ currentUser }),
}))
```

### 性能优化

#### 代码分割

- 路由级别的代码分割
- 组件懒加载
- 第三方库按需加载

#### 数据优化

- 虚拟化长列表
- 分页和无限滚动
- 数据缓存策略

#### 图片优化

- Next.js Image 组件
- WebP 格式优先
- 响应式图片

### 安全规范

#### 身份验证

- JWT Token 验证
- 自动刷新机制
- 安全的Token存储

#### 权限控制

- 基于角色的访问控制 (RBAC)
- 页面级权限验证
- API接口权限验证

#### 数据安全

- 敏感数据加密
- XSS 防护
- CSRF 防护

### 测试策略

#### 单元测试

- 组件测试 (Jest + Testing Library)
- 工具函数测试
- Hooks 测试

#### 集成测试

- API 集成测试
- 端到端测试 (Playwright)

#### 代码质量

- ESLint + Prettier
- TypeScript 严格模式
- 代码覆盖率要求 &gt;80%

### 部署配置

#### 环境变量

```env
NEXT_PUBLIC_API_URL=https://api.aura-esim.com
NEXT_PUBLIC_APP_ENV=production
NEXTAUTH_SECRET=your-secret-key
```

#### Docker 配置

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

---

## 总结

本PRD文档为AuraESIM后台管理面板提供了完整的设计方案，涵盖了功能架构、页面设计、组件规范和技术实现等各个方面。通过遵循现代化的设计原则和最佳实践，确保系统具有优秀的用户体验、高效的业务处理能力和良好的可维护性。

在实际开发过程中，建议：

1. 优先实现核心功能模块
2. 保持组件的可复用性和一致性
3. 重视性能优化和安全性
4. 建立完善的测试体系
5. 持续优化用户体验

此文档将作为开发团队的重要参考，确保项目的顺利实施和高质量交付。