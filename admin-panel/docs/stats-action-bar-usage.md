# StatsActionBar 组件使用指南

## 概述

`StatsActionBar` 是一个通用的统计信息和功能按钮组件，用于在各个管理页面中显示关键统计数据和主要操作按钮。该组件参考了现代管理界面的设计模式，提供简洁、一致的用户体验。

## 设计理念

基于截图中的布局思路，页面结构从上到下为：
1. **检索条件区域** - 筛选和搜索功能
2. **统计信息和功能按钮** - 关键数据展示和主要操作
3. **数据列表区域** - 详细数据表格

## 组件特性

### ✅ 核心功能
- 📊 **统计信息展示** - 支持多个关键指标的横向展示
- 🎯 **功能按钮集成** - 主要操作按钮的统一管理
- 🎨 **一致的视觉风格** - 遵循 shadcn/ui 设计规范
- 📱 **响应式设计** - 适配不同屏幕尺寸
- ⚡ **加载状态支持** - 优雅的骨架屏效果

### 🛠️ 技术特点
- TypeScript 类型安全
- 可复用的组件设计
- 灵活的配置选项
- 内置工具函数

## 使用方法

### 1. 基本用法

```tsx
import { StatsActionBar, StatItem, ActionButton } from '@/components/business/stats-action-bar'

// 准备统计数据
const statsData: StatItem[] = [
  {
    label: '总用户数',
    value: 1250,
    className: 'text-foreground'
  },
  {
    label: '活跃用户',
    value: 980,
    className: 'text-green-600'
  }
]

// 准备功能按钮
const actionButtons: ActionButton[] = [
  {
    label: '导出',
    icon: <Download className="h-4 w-4 mr-2" />,
    onClick: handleExport,
    variant: 'outline'
  },
  {
    label: '新建',
    icon: <Plus className="h-4 w-4 mr-2" />,
    onClick: handleCreate,
    variant: 'default'
  }
]

// 使用组件
<StatsActionBar
  stats={statsData}
  actions={actionButtons}
  loading={loading}
/>
```

### 2. 接口定义

#### StatItem 接口
```tsx
interface StatItem {
  label: string              // 统计项标签
  value: string | number     // 统计值
  subtext?: string          // 副文本（可选）
  className?: string        // 自定义样式类
}
```

#### ActionButton 接口
```tsx
interface ActionButton {
  label: string                    // 按钮文本
  icon?: React.ReactNode          // 图标（可选）
  onClick: () => void             // 点击事件
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive'
  size?: 'default' | 'sm' | 'lg'
  disabled?: boolean              // 是否禁用
  className?: string              // 自定义样式类
}
```

### 3. 预设样式

组件提供了预设的统计项样式：

```tsx
import { statStyles } from '@/components/business/stats-action-bar'

const statsData: StatItem[] = [
  {
    label: '成功',
    value: 100,
    className: statStyles.success  // 绿色
  },
  {
    label: '警告',
    value: 50,
    className: statStyles.warning  // 黄色
  },
  {
    label: '错误',
    value: 10,
    className: statStyles.danger   // 红色
  }
]
```

### 4. 工具函数

组件提供了实用的格式化函数：

```tsx
import { formatNumber, formatCurrency } from '@/components/business/stats-action-bar'

// 数字格式化
formatNumber(1500)     // "1.5K"
formatNumber(1500000)  // "1.5M"

// 货币格式化
formatCurrency(1234.56)        // "$1234.56"
formatCurrency(1234.56, '¥')   // "¥1234.56"
```

## 实际应用示例

### 用户管理页面
```tsx
const getUserStats = (): StatItem[] => [
  {
    label: '总用户数',
    value: stats.total,
    className: 'text-foreground'
  },
  {
    label: '活跃用户',
    value: stats.active,
    className: 'text-green-600'
  },
  {
    label: '待审核',
    value: stats.pending,
    className: 'text-yellow-600'
  },
  {
    label: '已暂停',
    value: stats.suspended,
    className: 'text-red-600'
  }
]

const getUserActions = (): ActionButton[] => [
  {
    label: '导出',
    icon: <Download className="h-4 w-4 mr-2" />,
    onClick: handleExport,
    variant: 'outline'
  },
  {
    label: '新建用户',
    icon: <UserPlus className="h-4 w-4 mr-2" />,
    onClick: handleCreateUser,
    variant: 'default'
  }
]
```

### 数据包管理页面
```tsx
const getPackageStats = (): StatItem[] => [
  {
    label: '总套餐数',
    value: stats.total,
    className: 'text-foreground'
  },
  {
    label: '活跃套餐',
    value: stats.active,
    className: 'text-green-600'
  },
  {
    label: '总收入',
    value: formatCurrency(stats.revenue),
    className: 'text-blue-600'
  }
]
```

## 最佳实践

### 1. 统计项数量
- 建议显示 2-5 个关键统计项
- 避免信息过载，保持界面简洁

### 2. 功能按钮
- 主要操作使用 `default` 变体
- 次要操作使用 `outline` 变体
- 危险操作使用 `destructive` 变体

### 3. 响应式考虑
- 在小屏幕上，统计项会自动换行
- 功能按钮会保持在右侧对齐

### 4. 加载状态
- 始终提供 `loading` 状态
- 骨架屏会自动匹配统计项和按钮数量

## 扩展性

组件设计具有良好的扩展性：

1. **自定义样式** - 通过 `className` 属性
2. **图标支持** - 任何 React 组件都可作为图标
3. **事件处理** - 灵活的点击事件处理
4. **类型安全** - 完整的 TypeScript 支持

## 总结

`StatsActionBar` 组件提供了一个标准化的方式来展示页面统计信息和主要操作，确保了整个管理系统的一致性和可维护性。通过合理使用这个组件，可以快速构建出专业、美观的管理界面。
