'use client'

import { useState } from 'react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { UserAvatar } from './user-avatar'
import { UserStatusBadge } from './user-status-badge'
import { UserRoleBadge } from './user-role-badge'
import { DeleteConfirmationDialog } from './delete-confirmation-dialog'
import { User } from '@/types/auth'
import { MoreHorizontal, Eye, Edit, Trash2, UserCheck, UserX, Shield } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { cn } from '@/lib/utils'

interface UserTableProps {
  users: User[]
  loading?: boolean
  selectedUsers?: string[]
  onSelectionChange?: (selectedIds: string[]) => void
  onUserClick?: (user: User) => void
  onEditUser?: (user: User) => void
  onDeleteUser?: (user: User) => void
  onChangeUserStatus?: (user: User, status: string) => void
  className?: string
}

export function UserTable({
  users,
  loading = false,
  selectedUsers = [],
  onSelectionChange,
  onUserClick,
  onEditUser,
  onDeleteUser,
  onChangeUserStatus,
  className
}: UserTableProps) {
  const [sortBy, setSortBy] = useState<string>('createdAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; user: User | null }>({
    open: false,
    user: null
  })
  
  const handleSelectAll = (checked: boolean) => {
    if (onSelectionChange) {
      onSelectionChange(checked ? users.map(u => u.id) : [])
    }
  }
  
  const handleSelectUser = (userId: string, checked: boolean) => {
    if (onSelectionChange) {
      const newSelection = checked 
        ? [...selectedUsers, userId]
        : selectedUsers.filter(id => id !== userId)
      onSelectionChange(newSelection)
    }
  }
  
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: zhCN 
      })
    } catch {
      return '-'
    }
  }
  
  const handleDeleteClick = (user: User) => {
    setDeleteDialog({ open: true, user })
  }

  const handleDeleteConfirm = () => {
    if (deleteDialog.user && onDeleteUser) {
      onDeleteUser(deleteDialog.user)
      setDeleteDialog({ open: false, user: null })
    }
  }

  const isAllSelected = users.length > 0 && selectedUsers.length === users.length
  const isPartiallySelected = selectedUsers.length > 0 && selectedUsers.length < users.length
  
  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="h-10 w-10 bg-muted rounded-full animate-pulse" />
                <div className="space-y-2 flex-1">
                  <div className="h-4 bg-muted rounded animate-pulse w-1/4" />
                  <div className="h-3 bg-muted rounded animate-pulse w-1/3" />
                </div>
                <div className="h-6 bg-muted rounded animate-pulse w-16" />
                <div className="h-6 bg-muted rounded animate-pulse w-20" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }
  
  if (users.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-12 text-center">
          <div className="text-muted-foreground">
            <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">暂无用户数据</p>
            <p className="text-sm">调整筛选条件或创建新用户</p>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">
            用户列表 ({users.length})
          </CardTitle>
          
          {selectedUsers.length > 0 && (
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                已选择 {selectedUsers.length} 个用户
              </Badge>
              <Button variant="outline" size="sm">
                批量操作
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={isAllSelected}
                    indeterminate={isPartiallySelected}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>用户</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>注册时间</TableHead>
                <TableHead>最后登录</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            
            <TableBody>
              {users.map((user) => (
                <TableRow 
                  key={user.id}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => onUserClick?.(user)}
                >
                  <TableCell onClick={(e) => e.stopPropagation()}>
                    <Checkbox
                      checked={selectedUsers.includes(user.id)}
                      onCheckedChange={(checked) => handleSelectUser(user.id, checked as boolean)}
                    />
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <UserAvatar user={user} size="sm" showStatus />
                      <div className="min-w-0">
                        <p className="font-medium truncate">{user.name}</p>
                        <p className="text-sm text-muted-foreground truncate">
                          {user.email}
                        </p>
                        {user.mobile && (
                          <p className="text-xs text-muted-foreground">
                            {user.mobile}
                          </p>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <UserStatusBadge status={user.status} />
                  </TableCell>
                  
                  <TableCell className="text-sm text-muted-foreground">
                    {formatDate(user.createdAt)}
                  </TableCell>
                  
                  <TableCell className="text-sm text-muted-foreground">
                    {formatDate(user.lastLoginAt)}
                  </TableCell>
                  
                  <TableCell onClick={(e) => e.stopPropagation()}>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onUserClick?.(user)}>
                          <Eye className="h-4 w-4 mr-2" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onEditUser?.(user)}>
                          <Edit className="h-4 w-4 mr-2" />
                          编辑用户
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {user.status === 'ACTIVE' ? (
                          <DropdownMenuItem 
                            onClick={() => onChangeUserStatus?.(user, 'SUSPENDED')}
                          >
                            <UserX className="h-4 w-4 mr-2" />
                            暂停用户
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem 
                            onClick={() => onChangeUserStatus?.(user, 'ACTIVE')}
                          >
                            <UserCheck className="h-4 w-4 mr-2" />
                            激活用户
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteClick(user)}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          删除用户
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {/* 删除确认对话框 */}
      <DeleteConfirmationDialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog({ open, user: null })}
        title="删除用户"
        description={`您确定要删除用户 "${deleteDialog.user?.name}" 吗？此操作将永久删除该用户的所有数据，包括订单记录、eSIM记录等。`}
        itemName={deleteDialog.user?.name}
        onConfirm={handleDeleteConfirm}
      />
    </Card>
  )
}
