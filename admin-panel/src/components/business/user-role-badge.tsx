import { Badge } from '@/components/ui/badge'
import { UserRole } from '@/types/auth'
import { cn } from '@/lib/utils'
import { User, Users, Building, Shield } from 'lucide-react'

interface UserRoleBadgeProps {
  role: UserRole
  className?: string
  showIcon?: boolean
}

const roleConfig = {
  USER: {
    label: '用户',
    icon: User,
    variant: 'outline' as const,
    className: 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-50'
  },
  RESELLER: {
    label: '代理商',
    icon: Users,
    variant: 'outline' as const,
    className: 'bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-50'
  },
  ENTERPRISE: {
    label: '企业',
    icon: Building,
    variant: 'outline' as const,
    className: 'bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-50'
  },
  ADMIN: {
    label: '管理员',
    icon: Shield,
    variant: 'outline' as const,
    className: 'bg-red-50 text-red-700 border-red-200 hover:bg-red-50'
  }
}

export function UserRoleBadge({ role, className, showIcon = false }: UserRoleBadgeProps) {
  const config = roleConfig[role]
  const Icon = config.icon
  
  return (
    <Badge 
      variant={config.variant}
      className={cn(config.className, className)}
    >
      {showIcon && <Icon className="w-3 h-3 mr-1" />}
      {config.label}
    </Badge>
  )
}
