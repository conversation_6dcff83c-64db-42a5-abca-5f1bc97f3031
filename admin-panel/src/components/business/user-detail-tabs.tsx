'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { UserAvatar } from './user-avatar'
import { UserStatusBadge } from './user-status-badge'
import { UserRoleBadge } from './user-role-badge'
import { UserDetails } from '@/types/user'
import { 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  MapPin, 
  Globe, 
  CreditCard, 
  ShoppingCart, 
  Smartphone,
  Activity,
  Shield,
  Building
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

interface UserDetailTabsProps {
  user: UserDetails
  onEdit?: () => void
  onChangeStatus?: (status: string) => void
  className?: string
}

export function UserDetailTabs({ user, onEdit, onChangeStatus, className }: UserDetailTabsProps) {
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-'
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: zhCN 
      })
    } catch {
      return '-'
    }
  }
  
  const formatCurrency = (amount?: number) => {
    if (amount === undefined || amount === null) return '-'
    return `¥${amount.toFixed(2)}`
  }
  
  return (
    <div className={className}>
      {/* 用户基本信息头部 */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-4">
              <UserAvatar user={user} size="lg" showStatus />
              <div className="space-y-1">
                <h1 className="text-2xl font-bold">{user.name}</h1>
                <p className="text-muted-foreground">{user.email}</p>
                <div className="flex items-center gap-2">
                  <UserRoleBadge role={user.role} showIcon />
                  <UserStatusBadge status={user.status} />
                </div>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button variant="outline" onClick={onEdit}>
                编辑用户
              </Button>
              <Button 
                variant={user.status === 'ACTIVE' ? 'destructive' : 'default'}
                onClick={() => onChangeStatus?.(user.status === 'ACTIVE' ? 'SUSPENDED' : 'ACTIVE')}
              >
                {user.status === 'ACTIVE' ? '暂停用户' : '激活用户'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* 详细信息标签页 */}
      <Tabs defaultValue="basic" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="basic">基本信息</TabsTrigger>
          <TabsTrigger value="orders">订单记录</TabsTrigger>
          <TabsTrigger value="esims">eSIM记录</TabsTrigger>
          <TabsTrigger value="credits">积分记录</TabsTrigger>
          <TabsTrigger value="activity">活动日志</TabsTrigger>
        </TabsList>
        
        {/* 基本信息 */}
        <TabsContent value="basic" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* 个人信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  个人信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">用户ID</span>
                    <span className="font-mono text-sm">{user.id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">姓名</span>
                    <span>{user.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">邮箱</span>
                    <span>{user.email}</span>
                  </div>
                  {user.mobile && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">手机号</span>
                      <span>{user.mobile}</span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
            
            {/* 账户信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  账户信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">注册时间</span>
                    <span>{formatDate(user.createdAt)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">最后登录</span>
                    <span>{formatDate(user.lastLoginAt)}</span>
                  </div>

                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* 统计信息 */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <ShoppingCart className="h-4 w-4" />
                  订单统计
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">总订单数</span>
                  <span className="font-medium">{user.totalOrders || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">总消费</span>
                  <span className="font-medium">{formatCurrency(user.totalSpent)}</span>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <Smartphone className="h-4 w-4" />
                  eSIM统计
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">总eSIM数</span>
                  <span className="font-medium">{user.esimCount || 0}</span>
                </div>
              </CardContent>
            </Card>
            

          </div>
        </TabsContent>
        
        {/* 其他标签页内容 - 暂时显示占位符 */}
        <TabsContent value="orders">
          <Card>
            <CardContent className="p-12 text-center">
              <ShoppingCart className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">订单记录功能开发中...</p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="esims">
          <Card>
            <CardContent className="p-12 text-center">
              <Smartphone className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">eSIM记录功能开发中...</p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="credits">
          <Card>
            <CardContent className="p-12 text-center">
              <CreditCard className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">积分记录功能开发中...</p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="activity">
          <Card>
            <CardContent className="p-12 text-center">
              <Activity className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">活动日志功能开发中...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
