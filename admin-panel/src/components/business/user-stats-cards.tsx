import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Users, UserCheck, UserX, Clock, TrendingUp } from 'lucide-react'
import { UserStats } from '@/types/user'
import { cn } from '@/lib/utils'

interface UserStatsCardsProps {
  stats: UserStats
  loading?: boolean
  className?: string
}

interface StatCardProps {
  title: string
  value: number
  icon: React.ReactNode
  trend?: {
    value: number
    isPositive: boolean
  }
  className?: string
  valueClassName?: string
}

function StatCard({ title, value, icon, trend, className, valueClassName }: StatCardProps) {
  return (
    <Card className={cn('', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold mb-1">
          <span className={cn('', valueClassName)}>
            {value.toLocaleString()}
          </span>
        </div>
        {trend && (
          <div className="flex items-center text-xs">
            <TrendingUp 
              className={cn(
                'h-3 w-3 mr-1',
                trend.isPositive ? 'text-green-600' : 'text-red-600 rotate-180'
              )} 
            />
            <span className={cn(
              'font-medium',
              trend.isPositive ? 'text-green-600' : 'text-red-600'
            )}>
              {trend.isPositive ? '+' : ''}{trend.value}%
            </span>
            <span className="text-muted-foreground ml-1">vs 上月</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export function UserStatsCards({ stats, loading = false, className }: UserStatsCardsProps) {
  if (loading) {
    return (
      <div className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-4', className)}>
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 bg-muted rounded animate-pulse w-20" />
              <div className="h-4 w-4 bg-muted rounded animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded animate-pulse w-16 mb-1" />
              <div className="h-3 bg-muted rounded animate-pulse w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }
  
  return (
    <div className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-4', className)}>
      {/* 总用户数 */}
      <StatCard
        title="总用户数"
        value={stats.total}
        icon={<Users className="h-4 w-4" />}
        trend={{
          value: Math.round((stats.recentRegistrations / stats.total) * 100),
          isPositive: true
        }}
      />
      
      {/* 活跃用户 */}
      <StatCard
        title="活跃用户"
        value={stats.active}
        icon={<UserCheck className="h-4 w-4" />}
        valueClassName="text-green-600"
        trend={{
          value: Math.round((stats.recentLogins / stats.active) * 100),
          isPositive: true
        }}
      />
      
      {/* 待审核用户 */}
      <StatCard
        title="待审核"
        value={stats.pending}
        icon={<Clock className="h-4 w-4" />}
        valueClassName="text-yellow-600"
      />
      
      {/* 暂停用户 */}
      <StatCard
        title="暂停用户"
        value={stats.suspended}
        icon={<UserX className="h-4 w-4" />}
        valueClassName="text-red-600"
      />
    </div>
  )
}

// 角色分布卡片
export function UserRoleDistribution({ stats, className }: { stats: UserStats, className?: string }) {
  const roleData = [
    { label: '普通用户', value: stats.byRole.USER, color: 'bg-blue-500' },
    { label: '代理商', value: stats.byRole.RESELLER, color: 'bg-purple-500' },
    { label: '企业用户', value: stats.byRole.ENTERPRISE, color: 'bg-orange-500' },
    { label: '管理员', value: stats.byRole.ADMIN, color: 'bg-red-500' }
  ]
  
  const total = Object.values(stats.byRole).reduce((sum, count) => sum + count, 0)
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">用户角色分布</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {roleData.map((role) => {
          const percentage = total > 0 ? (role.value / total) * 100 : 0
          
          return (
            <div key={role.label} className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="font-medium">{role.label}</span>
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">
                    {role.value} ({percentage.toFixed(1)}%)
                  </span>
                </div>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div 
                  className={cn('h-2 rounded-full transition-all', role.color)}
                  style={{ width: `${percentage}%` }}
                />
              </div>
            </div>
          )
        })}
      </CardContent>
    </Card>
  )
}
