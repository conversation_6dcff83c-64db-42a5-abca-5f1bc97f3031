'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'

// 统计项接口
export interface StatItem {
  label: string
  value: string | number
  subtext?: string
  className?: string
}

// 功能按钮接口
export interface ActionButton {
  label: string
  icon?: React.ReactNode
  onClick: () => void
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive'
  size?: 'default' | 'sm' | 'lg'
  disabled?: boolean
  className?: string
}

interface StatsActionBarProps {
  stats: StatItem[]
  actions?: ActionButton[]
  className?: string
  loading?: boolean
}

export function StatsActionBar({ 
  stats, 
  actions = [], 
  className,
  loading = false 
}: StatsActionBarProps) {
  if (loading) {
    return (
      <Card className={cn('', className)}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="space-y-1">
                  <div className="h-4 bg-muted rounded animate-pulse w-16" />
                  <div className="h-6 bg-muted rounded animate-pulse w-12" />
                </div>
              ))}
            </div>
            <div className="flex gap-2">
              {[...Array(2)].map((_, i) => (
                <div key={i} className="h-9 bg-muted rounded animate-pulse w-20" />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn('', className)}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* 左侧：统计信息 */}
          <div className="flex items-center gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="flex items-center gap-6">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground font-medium">
                    {stat.label}:
                  </p>
                  <p className={cn(
                    'text-lg font-semibold',
                    stat.className
                  )}>
                    {stat.value}
                    {stat.subtext && (
                      <span className="text-sm text-muted-foreground ml-1">
                        {stat.subtext}
                      </span>
                    )}
                  </p>
                </div>
                {index < stats.length - 1 && (
                  <Separator orientation="vertical" className="h-8" />
                )}
              </div>
            ))}
          </div>

          {/* 右侧：功能按钮 */}
          {actions.length > 0 && (
            <div className="flex items-center gap-2">
              {actions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || 'outline'}
                  size={action.size || 'sm'}
                  onClick={action.onClick}
                  disabled={action.disabled}
                  className={action.className}
                >
                  {action.icon}
                  {action.label}
                </Button>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// 预设的统计项样式
export const statStyles = {
  primary: 'text-primary',
  success: 'text-green-600',
  warning: 'text-yellow-600',
  danger: 'text-red-600',
  muted: 'text-muted-foreground'
}

// 工具函数：格式化数字
export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 工具函数：格式化货币
export function formatCurrency(amount: number, currency = '$'): string {
  return `${currency}${amount.toFixed(2)}`
}
