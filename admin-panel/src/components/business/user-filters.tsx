'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Search, Filter, X, Download, UserPlus } from 'lucide-react'
import { UserQueryParams, UserRole, UserStatus, UserExportOptions } from '@/types/user'
import { UserExportDialog } from './user-export-dialog'
import { cn } from '@/lib/utils'

interface UserFiltersProps {
  filters: UserQueryParams
  onFiltersChange: (filters: UserQueryParams) => void
  onExport?: (options: UserExportOptions) => void
  onCreateUser?: () => void
  className?: string
}

const roleOptions = [
  { value: 'ALL', label: '全部角色' },
  { value: 'USER', label: '用户' },
  { value: 'RESELLER', label: '代理商' },
  { value: 'ENTERPRISE', label: '企业' },
  { value: 'ADMIN', label: '管理员' }
]

const statusOptions = [
  { value: 'ALL', label: '全部状态' },
  { value: 'ACTIVE', label: '活跃' },
  { value: 'INACTIVE', label: '未激活' },
  { value: 'SUSPENDED', label: '已暂停' },
  { value: 'PENDING', label: '待审核' }
]

const sortOptions = [
  { value: 'createdAt', label: '注册时间' },
  { value: 'lastLoginAt', label: '最后登录' },
  { value: 'name', label: '姓名' },
  { value: 'email', label: '邮箱' }
]

export function UserFilters({ 
  filters, 
  onFiltersChange, 
  onExport,
  onCreateUser,
  className 
}: UserFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)
  
  const updateFilter = (key: keyof UserQueryParams, value: any) => {
    onFiltersChange({ ...filters, [key]: value, page: 1 })
  }
  
  const clearFilters = () => {
    onFiltersChange({
      page: 1,
      pageSize: filters.pageSize || 10
    })
  }
  
  const hasActiveFilters = Boolean(
    filters.search || 
    (filters.role && filters.role !== 'ALL') ||
    (filters.status && filters.status !== 'ALL') ||
    filters.dateFrom ||
    filters.dateTo
  )
  
  return (
    <Card className={cn('', className)}>
      <CardContent className="p-4 space-y-4">
        {/* 主要筛选行 */}
        <div className="flex flex-col sm:flex-row gap-4">
          {/* 搜索框 */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索用户姓名、邮箱或手机号..."
              value={filters.search || ''}
              onChange={(e) => updateFilter('search', e.target.value)}
              className="pl-10"
            />
          </div>
          
          {/* 角色筛选 */}
          <Select 
            value={filters.role || 'ALL'} 
            onValueChange={(value) => updateFilter('role', value === 'ALL' ? undefined : value)}
          >
            <SelectTrigger className="w-full sm:w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {roleOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {/* 状态筛选 */}
          <Select 
            value={filters.status || 'ALL'} 
            onValueChange={(value) => updateFilter('status', value === 'ALL' ? undefined : value)}
          >
            <SelectTrigger className="w-full sm:w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="whitespace-nowrap"
            >
              <Filter className="h-4 w-4 mr-2" />
              高级筛选
            </Button>
            
            {onExport && (
              <UserExportDialog onExport={onExport}>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
              </UserExportDialog>
            )}
            
            {onCreateUser && (
              <Button size="sm" onClick={onCreateUser}>
                <UserPlus className="h-4 w-4 mr-2" />
                新建用户
              </Button>
            )}
          </div>
        </div>
        
        {/* 高级筛选 */}
        {showAdvanced && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t">
            <div>
              <label className="text-sm font-medium mb-2 block">排序方式</label>
              <Select 
                value={filters.sortBy || 'createdAt'} 
                onValueChange={(value) => updateFilter('sortBy', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">排序顺序</label>
              <Select 
                value={filters.sortOrder || 'desc'} 
                onValueChange={(value) => updateFilter('sortOrder', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="desc">降序</SelectItem>
                  <SelectItem value="asc">升序</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">注册开始日期</label>
              <Input
                type="date"
                value={filters.dateFrom || ''}
                onChange={(e) => updateFilter('dateFrom', e.target.value)}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">注册结束日期</label>
              <Input
                type="date"
                value={filters.dateTo || ''}
                onChange={(e) => updateFilter('dateTo', e.target.value)}
              />
            </div>
          </div>
        )}
        
        {/* 活跃筛选标签 */}
        {hasActiveFilters && (
          <div className="flex items-center gap-2 flex-wrap">
            <span className="text-sm text-muted-foreground">活跃筛选:</span>
            
            {filters.search && (
              <Badge variant="secondary" className="gap-1">
                搜索: {filters.search}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => updateFilter('search', '')}
                />
              </Badge>
            )}
            
            {filters.role && filters.role !== 'ALL' && (
              <Badge variant="secondary" className="gap-1">
                角色: {roleOptions.find(r => r.value === filters.role)?.label}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => updateFilter('role', undefined)}
                />
              </Badge>
            )}
            
            {filters.status && filters.status !== 'ALL' && (
              <Badge variant="secondary" className="gap-1">
                状态: {statusOptions.find(s => s.value === filters.status)?.label}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => updateFilter('status', undefined)}
                />
              </Badge>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="h-6 px-2 text-xs"
            >
              清除全部
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
