import { Badge } from '@/components/ui/badge'
import { UserStatus } from '@/types/auth'
import { cn } from '@/lib/utils'

interface UserStatusBadgeProps {
  status: UserStatus
  className?: string
}

const statusConfig = {
  ACTIVE: {
    label: '活跃',
    variant: 'default' as const,
    className: 'bg-green-100 text-green-800 hover:bg-green-100'
  },
  INACTIVE: {
    label: '未激活',
    variant: 'secondary' as const,
    className: 'bg-gray-100 text-gray-800 hover:bg-gray-100'
  },
  SUSPENDED: {
    label: '已暂停',
    variant: 'destructive' as const,
    className: 'bg-red-100 text-red-800 hover:bg-red-100'
  },
  PENDING: {
    label: '待审核',
    variant: 'outline' as const,
    className: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100'
  }
}

export function UserStatusBadge({ status, className }: UserStatusBadgeProps) {
  const config = statusConfig[status]
  
  return (
    <Badge 
      variant={config.variant}
      className={cn(config.className, className)}
    >
      {config.label}
    </Badge>
  )
}
