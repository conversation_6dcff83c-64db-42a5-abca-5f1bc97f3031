'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Download, FileText, Table } from 'lucide-react'
import { UserExportOptions } from '@/types/user'
import { toast } from 'sonner'

interface UserExportDialogProps {
  onExport: (options: UserExportOptions) => void
  children?: React.ReactNode
}

const exportFields = [
  { key: 'id', label: '用户ID', default: true },
  { key: 'name', label: '姓名', default: true },
  { key: 'email', label: '邮箱', default: true },
  { key: 'mobile', label: '手机号', default: true },
  { key: 'role', label: '角色', default: true },
  { key: 'status', label: '状态', default: true },
  { key: 'createdAt', label: '注册时间', default: true },
  { key: 'lastLoginAt', label: '最后登录', default: false },
  { key: 'country', label: '国家', default: false },
  { key: 'timezone', label: '时区', default: false },
  { key: 'language', label: '语言', default: false },
  { key: 'totalOrders', label: '订单数', default: false },
  { key: 'totalSpent', label: '消费金额', default: false },
  { key: 'esimCount', label: 'eSIM数量', default: false }
]

export function UserExportDialog({ onExport, children }: UserExportDialogProps) {
  const [open, setOpen] = useState(false)
  const [format, setFormat] = useState<'csv' | 'excel'>('csv')
  const [selectedFields, setSelectedFields] = useState<string[]>(
    exportFields.filter(field => field.default).map(field => field.key)
  )
  const [includeStats, setIncludeStats] = useState(false)
  const [exporting, setExporting] = useState(false)

  const handleFieldToggle = (fieldKey: string, checked: boolean) => {
    if (checked) {
      setSelectedFields(prev => [...prev, fieldKey])
    } else {
      setSelectedFields(prev => prev.filter(key => key !== fieldKey))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedFields(exportFields.map(field => field.key))
    } else {
      setSelectedFields([])
    }
  }

  const handleExport = async () => {
    if (selectedFields.length === 0) {
      toast.error('请至少选择一个导出字段')
      return
    }

    setExporting(true)
    try {
      const options: UserExportOptions = {
        format,
        fields: selectedFields,
        includeStats
      }
      
      await onExport(options)
      setOpen(false)
      toast.success('用户数据导出成功')
    } catch (error) {
      toast.error('导出失败，请重试')
    } finally {
      setExporting(false)
    }
  }

  const isAllSelected = selectedFields.length === exportFields.length
  const isPartiallySelected = selectedFields.length > 0 && selectedFields.length < exportFields.length

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            导出
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            导出用户数据
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 导出格式 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">导出格式</CardTitle>
            </CardHeader>
            <CardContent>
              <Select value={format} onValueChange={(value: 'csv' | 'excel') => setFormat(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="csv">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      CSV 文件
                    </div>
                  </SelectItem>
                  <SelectItem value="excel">
                    <div className="flex items-center gap-2">
                      <Table className="h-4 w-4" />
                      Excel 文件
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* 导出字段 */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">导出字段</CardTitle>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="select-all"
                    checked={isAllSelected}
                    indeterminate={isPartiallySelected}
                    onCheckedChange={handleSelectAll}
                  />
                  <Label htmlFor="select-all" className="text-sm">
                    全选
                  </Label>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-3">
                {exportFields.map((field) => (
                  <div key={field.key} className="flex items-center space-x-2">
                    <Checkbox
                      id={field.key}
                      checked={selectedFields.includes(field.key)}
                      onCheckedChange={(checked) => handleFieldToggle(field.key, checked as boolean)}
                    />
                    <Label htmlFor={field.key} className="text-sm">
                      {field.label}
                    </Label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 附加选项 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">附加选项</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-stats"
                  checked={includeStats}
                  onCheckedChange={(checked) => setIncludeStats(checked as boolean)}
                />
                <Label htmlFor="include-stats" className="text-sm">
                  包含统计信息（订单数、消费金额等）
                </Label>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            取消
          </Button>
          <Button onClick={handleExport} disabled={exporting || selectedFields.length === 0}>
            {exporting ? '导出中...' : '开始导出'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
