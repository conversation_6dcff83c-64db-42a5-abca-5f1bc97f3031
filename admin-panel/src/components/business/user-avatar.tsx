import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { User } from '@/types/auth'
import { cn } from '@/lib/utils'

interface UserAvatarProps {
  user: User
  size?: 'sm' | 'md' | 'lg'
  className?: string
  showStatus?: boolean
}

const sizeConfig = {
  sm: 'h-8 w-8',
  md: 'h-10 w-10',
  lg: 'h-12 w-12'
}

const statusConfig = {
  ACTIVE: 'bg-green-500',
  INACTIVE: 'bg-gray-400',
  SUSPENDED: 'bg-red-500',
  PENDING: 'bg-yellow-500'
}

function getInitials(name: string): string {
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

export function UserAvatar({ 
  user, 
  size = 'md', 
  className, 
  showStatus = false 
}: UserAvatarProps) {
  const initials = getInitials(user.name)
  
  return (
    <div className={cn('relative', className)}>
      <Avatar className={cn(sizeConfig[size])}>
        <AvatarImage src={user.avatar} alt={user.name} />
        <AvatarFallback className="bg-muted font-medium">
          {initials}
        </AvatarFallback>
      </Avatar>
      
      {showStatus && (
        <div 
          className={cn(
            'absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full border-2 border-background',
            statusConfig[user.status]
          )}
        />
      )}
    </div>
  )
}
