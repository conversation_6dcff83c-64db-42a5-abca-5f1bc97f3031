'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog'
import { AlertTriangle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface DeleteConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description: string
  confirmText?: string
  itemName?: string
  onConfirm: () => void
  loading?: boolean
}

export function DeleteConfirmationDialog({
  open,
  onOpenChange,
  title,
  description,
  confirmText = "删除",
  itemName,
  onConfirm,
  loading = false
}: DeleteConfirmationDialogProps) {
  const [inputValue, setInputValue] = useState('')
  
  const isConfirmEnabled = itemName ? inputValue === itemName : true
  
  const handleConfirm = () => {
    if (isConfirmEnabled) {
      onConfirm()
    }
  }
  
  const handleClose = () => {
    setInputValue('')
    onOpenChange(false)
  }
  
  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-red-100">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <DialogTitle className="text-left">{title}</DialogTitle>
            </div>
          </div>
        </DialogHeader>
        
        <div className="space-y-4">
          <DialogDescription className="text-left">
            {description}
          </DialogDescription>
          
          {itemName && (
            <div className="space-y-2">
              <Label htmlFor="confirm-input" className="text-sm font-medium">
                请输入 <span className="font-mono bg-muted px-1 rounded">{itemName}</span> 以确认删除：
              </Label>
              <Input
                id="confirm-input"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={itemName}
                className={cn(
                  "font-mono",
                  inputValue && inputValue !== itemName && "border-red-300 focus:border-red-500"
                )}
              />
              {inputValue && inputValue !== itemName && (
                <p className="text-sm text-red-600">输入的名称不匹配</p>
              )}
            </div>
          )}
          
          <div className="rounded-md bg-red-50 p-3">
            <div className="text-sm text-red-800">
              <strong>警告：</strong>此操作无法撤销。删除后，所有相关数据将永久丢失。
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={loading}
          >
            取消
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={!isConfirmEnabled || loading}
          >
            {loading ? '删除中...' : confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
