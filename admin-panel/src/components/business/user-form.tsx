'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Separator } from '@/components/ui/separator'
import { UserAvatar } from './user-avatar'
import { User, UserRole, UserStatus } from '@/types/auth'
import { CreateUserRequest, UpdateUserRequest } from '@/types/user'
import { toast } from 'sonner'

// 表单验证模式 (基于API文档)
const userFormSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  name: z.string().min(1, '姓名不能为空'),
  mobile: z.string().optional(),
  password: z.string().min(8, '密码至少需要8个字符').optional()
})

type UserFormData = z.infer<typeof userFormSchema>

interface UserFormProps {
  user?: User // 编辑模式时传入
  onSubmit: (data: CreateUserRequest | UpdateUserRequest) => Promise<void>
  onCancel: () => void
  loading?: boolean
  className?: string
}



export function UserForm({ user, onSubmit, onCancel, loading = false, className }: UserFormProps) {
  const isEditMode = !!user
  
  const form = useForm<UserFormData>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      email: user?.email || '',
      name: user?.name || '',
      mobile: user?.mobile || '',
      password: ''
    }
  })

  const handleSubmit = async (data: UserFormData) => {
    try {
      await onSubmit(data)
      toast.success(isEditMode ? '用户更新成功' : '用户创建成功')
    } catch (error) {
      toast.error(isEditMode ? '用户更新失败' : '用户创建失败')
    }
  }

  return (
    <div className={className}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>邮箱地址 *</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="email"
                          placeholder="<EMAIL>"
                          disabled={isEditMode} // 编辑模式下邮箱不可修改
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>用户姓名 *</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="张三" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="mobile"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>手机号码</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="13800138000" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {!isEditMode && (
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>密码 *</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="password"
                            placeholder="至少8个字符"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>
            </CardContent>
          </Card>



          {/* 操作按钮 */}
          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? '保存中...' : (isEditMode ? '更新用户' : '创建用户')}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
