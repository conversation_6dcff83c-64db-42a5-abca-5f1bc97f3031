import { User, UserRole, UserStatus } from '@/types/auth'
import { UserDetails, UserStats, UserListResponse } from '@/types/user'

// 模拟用户数据
export const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: '张伟',
    firstName: '伟',
    lastName: '张',
    role: 'USER',
    status: 'ACTIVE',
    mobile: '+86 138 0013 8000',
    country: 'CN',
    timezone: 'Asia/Shanghai',
    language: 'zh-CN',
    emailVerified: true,
    mobileVerified: true,
    createdAt: '2024-01-15T08:30:00Z',
    lastLoginAt: '2024-01-20T14:22:00Z',
    totalOrders: 5,
    totalSpent: 299.50,
    esimCount: 3
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: '李明',
    firstName: '明',
    lastName: '李',
    role: 'RESELLER',
    status: 'ACTIVE',
    mobile: '+86 139 0013 9000',
    country: 'CN',
    timezone: 'Asia/Shanghai',
    language: 'zh-CN',
    emailVerified: true,
    mobileVerified: true,
    createdAt: '2023-12-01T10:15:00Z',
    lastLoginAt: '2024-01-20T16:45:00Z',
    totalOrders: 25,
    totalSpent: 1250.00,
    esimCount: 15
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: '王芳',
    firstName: '芳',
    lastName: '王',
    role: 'ENTERPRISE',
    status: 'ACTIVE',
    mobile: '+86 137 0013 7000',
    country: 'CN',
    timezone: 'Asia/Shanghai',
    language: 'zh-CN',
    emailVerified: true,
    mobileVerified: false,
    createdAt: '2023-11-20T09:00:00Z',
    lastLoginAt: '2024-01-20T11:30:00Z',
    totalOrders: 50,
    totalSpent: 5000.00,
    esimCount: 100
  },
  {
    id: '4',
    email: '<EMAIL>',
    name: '系统管理员',
    role: 'ADMIN',
    status: 'ACTIVE',
    mobile: '+86 136 0013 6000',
    country: 'CN',
    timezone: 'Asia/Shanghai',
    language: 'zh-CN',
    emailVerified: true,
    mobileVerified: true,
    createdAt: '2023-10-01T00:00:00Z',
    lastLoginAt: '2024-01-20T17:00:00Z',
    totalOrders: 0,
    totalSpent: 0,
    esimCount: 0
  },
  {
    id: '5',
    email: '<EMAIL>',
    name: '陈雷',
    firstName: '雷',
    lastName: '陈',
    role: 'USER',
    status: 'PENDING',
    mobile: '+86 135 0013 5000',
    country: 'CN',
    timezone: 'Asia/Shanghai',
    language: 'zh-CN',
    emailVerified: false,
    mobileVerified: false,
    createdAt: '2024-01-19T16:20:00Z',
    totalOrders: 0,
    totalSpent: 0,
    esimCount: 0
  },
  {
    id: '6',
    email: '<EMAIL>',
    name: '刘小丽',
    firstName: '小丽',
    lastName: '刘',
    role: 'USER',
    status: 'SUSPENDED',
    mobile: '+86 134 0013 4000',
    country: 'CN',
    timezone: 'Asia/Shanghai',
    language: 'zh-CN',
    emailVerified: true,
    mobileVerified: true,
    createdAt: '2024-01-10T12:00:00Z',
    lastLoginAt: '2024-01-18T09:15:00Z',
    totalOrders: 2,
    totalSpent: 89.90,
    esimCount: 1
  },
  {
    id: '7',
    email: '<EMAIL>',
    name: 'John Smith',
    firstName: 'John',
    lastName: 'Smith',
    role: 'USER',
    status: 'ACTIVE',
    mobile: '****** 0123 456',
    country: 'US',
    timezone: 'America/New_York',
    language: 'en-US',
    emailVerified: true,
    mobileVerified: true,
    createdAt: '2024-01-12T14:30:00Z',
    lastLoginAt: '2024-01-20T20:10:00Z',
    totalOrders: 8,
    totalSpent: 450.75,
    esimCount: 5
  },
  {
    id: '8',
    email: '<EMAIL>',
    name: '山田太郎',
    firstName: '太郎',
    lastName: '山田',
    role: 'RESELLER',
    status: 'ACTIVE',
    mobile: '+81 90 1234 5678',
    country: 'JP',
    timezone: 'Asia/Tokyo',
    language: 'ja-JP',
    emailVerified: true,
    mobileVerified: true,
    createdAt: '2023-12-15T06:45:00Z',
    lastLoginAt: '2024-01-20T23:30:00Z',
    totalOrders: 35,
    totalSpent: 2100.00,
    esimCount: 20
  }
]

// 模拟用户详情数据
export const mockUserDetails: Record<string, UserDetails> = {
  '1': {
    ...mockUsers[0],
    creditBalance: 150,
    totalCreditEarned: 500,
    totalCreditSpent: 350,
    totalOrders: 5,
    completedOrders: 4,
    cancelledOrders: 1,
    totalSpent: 299.50,
    esimCount: 3,
    activeEsims: 2,
    expiredEsims: 1,
    lastActivity: '2024-01-20T14:22:00Z',
    loginCount: 25
  },
  '2': {
    ...mockUsers[1],
    creditBalance: 800,
    totalCreditEarned: 2000,
    totalCreditSpent: 1200,
    totalOrders: 25,
    completedOrders: 23,
    cancelledOrders: 2,
    totalSpent: 1250.00,
    esimCount: 15,
    activeEsims: 12,
    expiredEsims: 3,
    lastActivity: '2024-01-20T16:45:00Z',
    loginCount: 120
  },
  '3': {
    ...mockUsers[2],
    creditBalance: 2500,
    totalCreditEarned: 10000,
    totalCreditSpent: 7500,
    totalOrders: 50,
    completedOrders: 48,
    cancelledOrders: 2,
    totalSpent: 5000.00,
    esimCount: 100,
    activeEsims: 85,
    expiredEsims: 15,
    lastActivity: '2024-01-20T11:30:00Z',
    loginCount: 200,
    department: '信息技术部',
    position: '技术总监',
    employeeId: 'EMP001'
  }
}

// 模拟用户统计数据
export const mockUserStats: UserStats = {
  total: 1250,
  active: 980,
  inactive: 150,
  suspended: 80,
  pending: 40,
  byRole: {
    USER: 1000,
    RESELLER: 180,
    ENTERPRISE: 60,
    ADMIN: 10
  },
  recentRegistrations: 45, // 最近30天
  recentLogins: 750 // 最近30天
}

// 模拟API响应函数
export function getMockUsers(params: {
  page?: number
  pageSize?: number
  search?: string
  role?: UserRole | 'ALL'
  status?: UserStatus | 'ALL'
}): UserListResponse {
  let filteredUsers = [...mockUsers]
  
  // 搜索过滤
  if (params.search) {
    const searchLower = params.search.toLowerCase()
    filteredUsers = filteredUsers.filter(user => 
      user.name.toLowerCase().includes(searchLower) ||
      user.email.toLowerCase().includes(searchLower) ||
      (user.mobile && user.mobile.includes(params.search!))
    )
  }
  
  // 角色过滤
  if (params.role && params.role !== 'ALL') {
    filteredUsers = filteredUsers.filter(user => user.role === params.role)
  }
  
  // 状态过滤
  if (params.status && params.status !== 'ALL') {
    filteredUsers = filteredUsers.filter(user => user.status === params.status)
  }
  
  // 分页
  const page = params.page || 1
  const pageSize = params.pageSize || 10
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedUsers = filteredUsers.slice(startIndex, endIndex)
  
  return {
    users: paginatedUsers,
    total: filteredUsers.length,
    page,
    pageSize,
    totalPages: Math.ceil(filteredUsers.length / pageSize)
  }
}

export function getMockUserDetails(userId: string): UserDetails | null {
  return mockUserDetails[userId] || null
}

export function getMockUserStats(): UserStats {
  return mockUserStats
}
