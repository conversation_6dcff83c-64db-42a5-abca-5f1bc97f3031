import { User, UserRole, UserStatus } from '@/types/auth'
import { UserDetails, UserStats, UserListResponse } from '@/types/user'

// 模拟用户数据 (基于API文档)
export const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: '张伟',
    role: 'USER',
    status: 'ACTIVE',
    mobile: '13800138000',
    createdAt: '2024-01-15T08:30:00Z',
    lastLoginAt: '2024-01-20T14:22:00Z'
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: '李明',
    role: 'USER',
    status: 'ACTIVE',
    mobile: '13900139000',
    createdAt: '2023-12-01T10:15:00Z',
    lastLoginAt: '2024-01-20T16:45:00Z'
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: '王芳',
    role: 'USER',
    status: 'ACTIVE',
    mobile: '13700137000',
    createdAt: '2023-11-20T09:00:00Z',
    lastLoginAt: '2024-01-20T11:30:00Z'
  },
  {
    id: '4',
    email: '<EMAIL>',
    name: '陈雷',
    role: 'USER',
    status: 'PENDING',
    mobile: '13500135000',
    createdAt: '2024-01-19T16:20:00Z'
  },
  {
    id: '5',
    email: '<EMAIL>',
    name: '刘小丽',
    role: 'USER',
    status: 'SUSPENDED',
    mobile: '13400134000',
    createdAt: '2024-01-10T12:00:00Z',
    lastLoginAt: '2024-01-18T09:15:00Z'
  },
  {
    id: '6',
    email: '<EMAIL>',
    name: 'John Smith',
    role: 'USER',
    status: 'ACTIVE',
    mobile: '****** 0123 456',
    createdAt: '2024-01-12T14:30:00Z',
    lastLoginAt: '2024-01-20T20:10:00Z'
  },
  {
    id: '7',
    email: '<EMAIL>',
    name: '山田太郎',
    role: 'USER',
    status: 'ACTIVE',
    mobile: '+81 90 1234 5678',
    createdAt: '2023-12-15T06:45:00Z',
    lastLoginAt: '2024-01-20T23:30:00Z'
  },
  {
    id: '8',
    email: '<EMAIL>',
    name: 'Maria Garcia',
    role: 'USER',
    status: 'INACTIVE',
    mobile: '+34 600 123 456',
    createdAt: '2024-01-05T10:00:00Z'
  }
]

// 模拟用户详情数据 (基于API文档)
export const mockUserDetails: Record<string, UserDetails> = {
  '1': {
    ...mockUsers[0],
    totalOrders: 5,
    totalSpent: 299.50,
    esimCount: 3
  },
  '2': {
    ...mockUsers[1],
    totalOrders: 8,
    totalSpent: 450.75,
    esimCount: 5
  },
  '3': {
    ...mockUsers[2],
    totalOrders: 12,
    totalSpent: 680.20,
    esimCount: 8
  }
}

// 模拟用户统计数据 (简化版)
export const mockUserStats: UserStats = {
  total: 8,
  active: 5,
  inactive: 1,
  suspended: 1,
  pending: 1
}

// 模拟API响应函数
export function getMockUsers(params: {
  page?: number
  pageSize?: number
  search?: string
  role?: UserRole | 'ALL'
  status?: UserStatus | 'ALL'
}): UserListResponse {
  let filteredUsers = [...mockUsers]
  
  // 搜索过滤
  if (params.search) {
    const searchLower = params.search.toLowerCase()
    filteredUsers = filteredUsers.filter(user => 
      user.name.toLowerCase().includes(searchLower) ||
      user.email.toLowerCase().includes(searchLower) ||
      (user.mobile && user.mobile.includes(params.search!))
    )
  }
  

  
  // 状态过滤
  if (params.status && params.status !== 'ALL') {
    filteredUsers = filteredUsers.filter(user => user.status === params.status)
  }
  
  // 分页
  const page = params.page || 1
  const pageSize = params.pageSize || 10
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedUsers = filteredUsers.slice(startIndex, endIndex)
  
  return {
    users: paginatedUsers,
    total: filteredUsers.length,
    page,
    pageSize,
    totalPages: Math.ceil(filteredUsers.length / pageSize)
  }
}

export function getMockUserDetails(userId: string): UserDetails | null {
  return mockUserDetails[userId] || null
}

export function getMockUserStats(): UserStats {
  return mockUserStats
}
