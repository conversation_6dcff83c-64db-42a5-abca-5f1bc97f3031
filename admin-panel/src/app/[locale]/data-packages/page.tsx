'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import PageGuard from '@/components/layout/PageGuard'
import { StatsActionBar, StatItem, ActionButton } from '@/components/business/stats-action-bar'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useTranslations } from 'next-intl'
import { Package, Plus, Download, Settings } from 'lucide-react'
import { toast } from 'sonner'

// 模拟数据包统计
interface DataPackageStats {
  total: number
  active: number
  pending: number
  revenue: number
}

export default function DataPackagesPage() {
  const t = useTranslations('dataPackage')
  const router = useRouter()
  
  const [stats, setStats] = useState<DataPackageStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadStats()
  }, [])

  const loadStats = async () => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800))
      
      setStats({
        total: 156,
        active: 142,
        pending: 14,
        revenue: 25680.50
      })
    } catch (error) {
      toast.error('加载统计数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 准备统计信息数据
  const getStatsData = (): StatItem[] => {
    if (!stats) return []
    
    return [
      {
        label: '总套餐数',
        value: stats.total,
        className: 'text-foreground'
      },
      {
        label: '活跃套餐',
        value: stats.active,
        className: 'text-green-600'
      },
      {
        label: '待审核',
        value: stats.pending,
        className: 'text-yellow-600'
      },
      {
        label: '总收入',
        value: `$${stats.revenue.toLocaleString()}`,
        className: 'text-blue-600'
      }
    ]
  }

  // 准备功能按钮数据
  const getActionButtons = (): ActionButton[] => [
    {
      label: '导出',
      icon: <Download className="h-4 w-4 mr-2" />,
      onClick: () => toast.success('数据包导出功能'),
      variant: 'outline'
    },
    {
      label: '批量设置',
      icon: <Settings className="h-4 w-4 mr-2" />,
      onClick: () => toast.success('批量设置功能'),
      variant: 'outline'
    },
    {
      label: '新建套餐',
      icon: <Plus className="h-4 w-4 mr-2" />,
      onClick: () => router.push('/data-packages/create'),
      variant: 'default'
    }
  ]

  return (
    <PageGuard>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">数据包管理</h1>
          <p className="text-muted-foreground">
            管理和配置各种数据套餐，设置价格、流量和有效期
          </p>
        </div>

        {/* 统计信息和功能按钮 */}
        <StatsActionBar
          stats={getStatsData()}
          actions={getActionButtons()}
          loading={loading}
        />

        {/* 筛选器区域 */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="text-sm text-muted-foreground">
                筛选器组件将在这里显示...
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 数据列表区域 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              数据包列表
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12 text-muted-foreground">
              <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">数据包列表功能开发中</p>
              <p className="text-sm">数据包管理功能将在后续版本中实现</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </PageGuard>
  )
}
