'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import PageGuard from '@/components/layout/PageGuard'
import { UserFilters } from '@/components/business/user-filters'
import { UserTable } from '@/components/business/user-table'
import { UserStatsCards, UserRoleDistribution } from '@/components/business/user-stats-cards'
import { DataPagination } from '@/components/business/data-pagination'
import { useTranslations } from 'next-intl'
import { User } from '@/types/auth'
import { UserQueryParams, UserStats, UserExportOptions } from '@/types/user'
import { getMockUsers, getMockUserStats } from '@/lib/mock-data/users'
import { toast } from 'sonner'

export default function UsersPage() {
  const t = useTranslations('user')
  const router = useRouter()

  // 状态管理
  const [users, setUsers] = useState<User[]>([])
  const [stats, setStats] = useState<UserStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [filters, setFilters] = useState<UserQueryParams>({
    page: 1,
    pageSize: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  })
  const [pagination, setPagination] = useState({
    total: 0,
    totalPages: 0
  })

  // 加载数据
  useEffect(() => {
    loadUsers()
    loadStats()
  }, [filters])

  const loadUsers = async () => {
    setLoading(true)
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      const response = getMockUsers(filters)
      setUsers(response.users)
      setPagination({
        total: response.total,
        totalPages: response.totalPages
      })
    } catch (error) {
      toast.error('加载用户数据失败')
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const statsData = getMockUserStats()
      setStats(statsData)
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  }

  // 事件处理
  const handleFiltersChange = (newFilters: UserQueryParams) => {
    setFilters(newFilters)
    setSelectedUsers([]) // 清除选择
  }

  const handleUserClick = (user: User) => {
    router.push(`/users/${user.id}`)
  }

  const handleEditUser = (user: User) => {
    router.push(`/users/${user.id}/edit`)
  }

  const handleDeleteUser = (user: User) => {
    // TODO: 实现删除用户功能
    toast.success(`删除用户 ${user.name} 成功`)
  }

  const handleChangeUserStatus = (user: User, status: string) => {
    // TODO: 实现状态变更功能
    toast.success(`用户 ${user.name} 状态已更新为 ${status}`)
  }

  const handleExport = async (options: UserExportOptions) => {
    try {
      // 模拟导出API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      console.log('导出选项:', options)
      console.log('当前筛选条件:', filters)

      // 模拟文件下载
      const filename = `users_export_${new Date().toISOString().split('T')[0]}.${options.format}`
      toast.success(`用户数据已导出为 ${filename}`)
    } catch (error) {
      toast.error('导出失败，请重试')
      throw error
    }
  }

  const handleCreateUser = () => {
    router.push('/users/create')
  }

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }))
  }

  const handlePageSizeChange = (pageSize: number) => {
    setFilters(prev => ({ ...prev, pageSize, page: 1 }))
  }

  return (
    <PageGuard>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('userManagement')}</h1>
          <p className="text-muted-foreground">
            管理系统中的所有用户账户，包括个人用户、代理商、企业用户和管理员
          </p>
        </div>

        {/* 统计卡片 */}
        {stats && (
          <div className="grid gap-6 lg:grid-cols-4">
            <div className="lg:col-span-3">
              <UserStatsCards stats={stats} />
            </div>
            <div className="lg:col-span-1">
              <UserRoleDistribution stats={stats} />
            </div>
          </div>
        )}

        {/* 筛选器 */}
        <UserFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
          onExport={handleExport}
          onCreateUser={handleCreateUser}
        />

        {/* 用户表格 */}
        <UserTable
          users={users}
          loading={loading}
          selectedUsers={selectedUsers}
          onSelectionChange={setSelectedUsers}
          onUserClick={handleUserClick}
          onEditUser={handleEditUser}
          onDeleteUser={handleDeleteUser}
          onChangeUserStatus={handleChangeUserStatus}
        />

        {/* 分页 */}
        {pagination.totalPages > 1 && (
          <DataPagination
            currentPage={filters.page || 1}
            totalPages={pagination.totalPages}
            totalItems={pagination.total}
            pageSize={filters.pageSize || 10}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        )}
      </div>
    </PageGuard>
  )
}
