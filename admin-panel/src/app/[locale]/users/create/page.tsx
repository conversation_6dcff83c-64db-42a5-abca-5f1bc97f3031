'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import PageGuard from '@/components/layout/PageGuard'
import { UserForm } from '@/components/business/user-form'
import { ArrowLeft } from 'lucide-react'
import { CreateUserRequest } from '@/types/user'
import { toast } from 'sonner'
import { useTranslations } from 'next-intl'

export default function CreateUserPage() {
  const router = useRouter()
  const t = useTranslations('user')
  
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (data: CreateUserRequest) => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      console.log('创建用户:', data)
      
      // 模拟成功响应
      toast.success('用户创建成功')
      router.push('/users')
    } catch (error) {
      toast.error('用户创建失败')
      throw error
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    router.back()
  }

  return (
    <PageGuard>
      <div className="space-y-6">
        {/* 返回按钮和标题 */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={handleCancel}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('addUser')}</h1>
            <p className="text-muted-foreground">
              创建新的用户账户
            </p>
          </div>
        </div>

        {/* 用户表单 */}
        <UserForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={loading}
        />
      </div>
    </PageGuard>
  )
}
