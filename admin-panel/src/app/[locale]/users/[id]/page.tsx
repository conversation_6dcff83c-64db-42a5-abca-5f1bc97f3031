'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import PageGuard from '@/components/layout/PageGuard'
import { UserDetailTabs } from '@/components/business/user-detail-tabs'
import { ArrowLeft, Loader2 } from 'lucide-react'
import { UserDetails } from '@/types/user'
import { getMockUserDetails } from '@/lib/mock-data/users'
import { toast } from 'sonner'
import { useTranslations } from 'next-intl'

export default function UserDetailPage() {
  const router = useRouter()
  const params = useParams()
  const t = useTranslations('user')
  
  const [user, setUser] = useState<UserDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [notFound, setNotFound] = useState(false)

  const userId = params.id as string

  useEffect(() => {
    loadUserDetails()
  }, [userId])

  const loadUserDetails = async () => {
    setLoading(true)
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 800))
      
      const userDetails = getMockUserDetails(userId)
      if (userDetails) {
        setUser(userDetails)
      } else {
        setNotFound(true)
      }
    } catch (error) {
      toast.error('加载用户详情失败')
      setNotFound(true)
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = () => {
    router.push(`/users/${userId}/edit`)
  }

  const handleChangeStatus = (status: string) => {
    // TODO: 实现状态变更功能
    toast.success(`用户状态已更新为 ${status}`)
    if (user) {
      setUser({
        ...user,
        status: status as any
      })
    }
  }

  const handleBack = () => {
    router.back()
  }

  if (loading) {
    return (
      <PageGuard>
        <div className="space-y-6">
          {/* 返回按钮 */}
          <Button variant="ghost" onClick={handleBack} className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回用户列表
          </Button>

          {/* 加载状态 */}
          <Card>
            <CardContent className="p-12 text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">正在加载用户详情...</p>
            </CardContent>
          </Card>
        </div>
      </PageGuard>
    )
  }

  if (notFound || !user) {
    return (
      <PageGuard>
        <div className="space-y-6">
          {/* 返回按钮 */}
          <Button variant="ghost" onClick={handleBack} className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回用户列表
          </Button>

          {/* 未找到状态 */}
          <Card>
            <CardContent className="p-12 text-center">
              <div className="text-muted-foreground">
                <h2 className="text-xl font-semibold mb-2">用户不存在</h2>
                <p className="mb-4">未找到ID为 {userId} 的用户</p>
                <Button onClick={handleBack}>
                  返回用户列表
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageGuard>
    )
  }

  return (
    <PageGuard>
      <div className="space-y-6">
        {/* 返回按钮 */}
        <Button variant="ghost" onClick={handleBack} className="mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          返回用户列表
        </Button>

        {/* 用户详情 */}
        <UserDetailTabs
          user={user}
          onEdit={handleEdit}
          onChangeStatus={handleChangeStatus}
        />
      </div>
    </PageGuard>
  )
}
