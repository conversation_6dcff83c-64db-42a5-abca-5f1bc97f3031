'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import PageGuard from '@/components/layout/PageGuard'
import { UserForm } from '@/components/business/user-form'
import { ArrowLeft, Loader2 } from 'lucide-react'
import { User } from '@/types/auth'
import { UpdateUserRequest } from '@/types/user'
import { getMockUserDetails } from '@/lib/mock-data/users'
import { toast } from 'sonner'
import { useTranslations } from 'next-intl'

export default function EditUserPage() {
  const router = useRouter()
  const params = useParams()
  const t = useTranslations('user')
  
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [notFound, setNotFound] = useState(false)

  const userId = params.id as string

  useEffect(() => {
    loadUser()
  }, [userId])

  const loadUser = async () => {
    setLoading(true)
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const userDetails = getMockUserDetails(userId)
      if (userDetails) {
        setUser(userDetails)
      } else {
        setNotFound(true)
      }
    } catch (error) {
      toast.error('加载用户信息失败')
      setNotFound(true)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (data: UpdateUserRequest) => {
    setSubmitting(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      console.log('更新用户:', { userId, data })
      
      // 模拟成功响应
      toast.success('用户更新成功')
      router.push(`/users/${userId}`)
    } catch (error) {
      toast.error('用户更新失败')
      throw error
    } finally {
      setSubmitting(false)
    }
  }

  const handleCancel = () => {
    router.back()
  }

  if (loading) {
    return (
      <PageGuard>
        <div className="space-y-6">
          {/* 返回按钮 */}
          <Button variant="ghost" onClick={handleCancel}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>

          {/* 加载状态 */}
          <Card>
            <CardContent className="p-12 text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
              <p className="text-muted-foreground">正在加载用户信息...</p>
            </CardContent>
          </Card>
        </div>
      </PageGuard>
    )
  }

  if (notFound || !user) {
    return (
      <PageGuard>
        <div className="space-y-6">
          {/* 返回按钮 */}
          <Button variant="ghost" onClick={handleCancel}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>

          {/* 未找到状态 */}
          <Card>
            <CardContent className="p-12 text-center">
              <div className="text-muted-foreground">
                <h2 className="text-xl font-semibold mb-2">用户不存在</h2>
                <p className="mb-4">未找到ID为 {userId} 的用户</p>
                <Button onClick={handleCancel}>
                  返回
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageGuard>
    )
  }

  return (
    <PageGuard>
      <div className="space-y-6">
        {/* 返回按钮和标题 */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={handleCancel}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{t('editUser')}</h1>
            <p className="text-muted-foreground">
              编辑用户 {user.name} 的信息
            </p>
          </div>
        </div>

        {/* 用户表单 */}
        <UserForm
          user={user}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={submitting}
        />
      </div>
    </PageGuard>
  )
}
