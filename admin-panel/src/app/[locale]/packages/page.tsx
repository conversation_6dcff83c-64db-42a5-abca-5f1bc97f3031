'use client'

import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import PageGuard from '@/components/layout/PageGuard'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Search,
  RefreshCw,
  Eye,
  Package as PackageIcon
} from 'lucide-react'
import { PackageService } from '@/services/packageService'
import type { Package, Provider, PackageQueryParams } from '@/types/package'

export default function PackagesPage() {
  const t = useTranslations('package')
  const tCommon = useTranslations('common')
  const router = useRouter()

  const [packages, setPackages] = useState<Package[]>([])
  const [filteredPackages, setFilteredPackages] = useState<Package[]>([])
  const [providers, setProviders] = useState<Provider[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProvider, setSelectedProvider] = useState<string>('all')
  const [selectedRegion, setSelectedRegion] = useState<string>('all')
  const [selectedDataSize, setSelectedDataSize] = useState<string>('')

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true)

      // 模拟提供商数据
      setProviders([
        { id: '1', name: 'ESIMAccess', type: 'esimaccess', status: 'ACTIVE' as any },
        { id: '2', name: 'MayaMobile', type: 'mayamobile', status: 'ACTIVE' as any },
        { id: '3', name: 'GlobaleSIM', type: 'globalesim', status: 'ACTIVE' as any }
      ])

      // 加载套餐数据
      await loadPackages()
    } catch (error) {
      console.error('Failed to load data:', error)
    } finally {
      setLoading(false)
    }
  }

  // 加载套餐列表
  const loadPackages = async () => {
    try {
      // 模拟更多套餐数据
      const mockPackages: Package[] = [
        {
          id: 'pkg_001',
          name: 'Aaland Islands 10GB 7Days',
          description: 'Aaland Islands 10GB 7Days',
          price: 180,
          currency: '$',
          dataVolume: 10 * 1024 * 1024 * 1024,
          validityDays: 7,
          locationCodes: ['AX'],
          supportsSMS: false,
          dataType: 'LIMITED',
          networkTypes: ['3G', '4G', '5G'],
          supportTopUp: false,
          provider: 'ESIMAccess'
        },
        {
          id: 'pkg_002',
          name: 'Aaland Islands 20GB 30Days',
          description: 'Aaland Islands 20GB 30Days',
          price: 2000,
          currency: '$',
          dataVolume: 20 * 1024 * 1024 * 1024,
          validityDays: 30,
          locationCodes: ['AX'],
          supportsSMS: false,
          dataType: 'LIMITED',
          networkTypes: ['3G', '4G', '5G'],
          supportTopUp: false,
          provider: 'ESIMAccess'
        },
        {
          id: 'pkg_003',
          name: 'Aaland Islands 3GB 15Days',
          description: 'Aaland Islands 3GB 15Days',
          price: 460,
          currency: '$',
          dataVolume: 3 * 1024 * 1024 * 1024,
          validityDays: 15,
          locationCodes: ['AX'],
          supportsSMS: false,
          dataType: 'LIMITED',
          networkTypes: ['3G', '4G', '5G'],
          supportTopUp: false,
          provider: 'ESIMAccess'
        },
        {
          id: 'pkg_004',
          name: 'Aaland Islands 3GB 30Days',
          description: 'Aaland Islands 3GB 30Days',
          price: 470,
          currency: '$',
          dataVolume: 3 * 1024 * 1024 * 1024,
          validityDays: 30,
          locationCodes: ['AX'],
          supportsSMS: false,
          dataType: 'LIMITED',
          networkTypes: ['3G', '4G', '5G'],
          supportTopUp: false,
          provider: 'ESIMAccess'
        },
        {
          id: 'pkg_005',
          name: 'Europe 5GB 30Days',
          description: 'Europe 5GB 30Days',
          price: 1200,
          currency: '$',
          dataVolume: 5 * 1024 * 1024 * 1024,
          validityDays: 30,
          locationCodes: ['EU'],
          supportsSMS: true,
          dataType: 'LIMITED',
          networkTypes: ['4G', '5G'],
          supportTopUp: true,
          provider: 'MayaMobile'
        },
        {
          id: 'pkg_006',
          name: 'Asia 3GB 15Days',
          description: 'Asia 3GB 15Days',
          price: 800,
          currency: '$',
          dataVolume: 3 * 1024 * 1024 * 1024,
          validityDays: 15,
          locationCodes: ['AS'],
          supportsSMS: false,
          dataType: 'LIMITED',
          networkTypes: ['4G'],
          supportTopUp: false,
          provider: 'MayaMobile'
        }
      ]

      setPackages(mockPackages)
      setFilteredPackages(mockPackages)
    } catch (error) {
      console.error('Failed to load packages:', error)
      setPackages([])
      setFilteredPackages([])
    }
  }

  // 应用筛选
  const applyFilters = () => {
    let filtered = [...packages]

    // 搜索筛选
    if (searchQuery.trim()) {
      filtered = filtered.filter(pkg =>
        pkg.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        pkg.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        pkg.locationCodes.some(code =>
          code.toLowerCase().includes(searchQuery.toLowerCase())
        )
      )
    }

    // 提供商筛选
    if (selectedProvider !== 'all') {
      filtered = filtered.filter(pkg => pkg.provider === selectedProvider)
    }

    // 地区筛选
    if (selectedRegion !== 'all') {
      filtered = filtered.filter(pkg =>
        pkg.locationCodes.some(code => code === selectedRegion)
      )
    }

    // 数据包大小筛选
    if (selectedDataSize.trim() && !isNaN(Number(selectedDataSize))) {
      const inputSizeInGB = Number(selectedDataSize)
      filtered = filtered.filter(pkg => {
        const pkgSizeInGB = Math.round(pkg.dataVolume / (1024 * 1024 * 1024))
        return pkgSizeInGB === inputSizeInGB
      })
    }

    setFilteredPackages(filtered)
  }

  // 清除筛选
  const clearFilters = () => {
    setSearchQuery('')
    setSelectedProvider('all')
    setSelectedRegion('all')
    setSelectedDataSize('')
    setFilteredPackages(packages)
  }

  // 同步套餐数据
  const handleSync = async () => {
    try {
      setLoading(true)
      await new Promise(resolve => setTimeout(resolve, 1000))
      await loadData()
    } catch (error) {
      console.error('Failed to sync packages:', error)
    } finally {
      setLoading(false)
    }
  }

  // 格式化数据量
  const formatDataVolume = (bytes: number): string => {
    if (bytes === 0) return t('unlimited')
    const gb = bytes / (1024 * 1024 * 1024)
    return `${gb.toFixed(0)}GB`
  }

  // 查看详情
  const handleViewDetails = (pkg: Package) => {
    router.push(`/packages/${pkg.provider || 'default'}/${pkg.id}`)
  }

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    applyFilters()
  }, [packages, searchQuery, selectedProvider, selectedRegion, selectedDataSize])

  return (
    <PageGuard>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">{t('packages')}</h1>
            <p className="text-muted-foreground">
              {tCommon('total')} {filteredPackages.length} {t('packages')}
            </p>
          </div>
          <Button onClick={handleSync} disabled={loading} variant="outline">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {t('syncPackages')}
          </Button>
        </div>

        {/* 筛选条件 */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex gap-4 items-end">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Name"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <Select value={selectedRegion} onValueChange={setSelectedRegion}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Region" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{tCommon('all')}</SelectItem>
                  <SelectItem value="AX">Aaland Islands</SelectItem>
                  <SelectItem value="EU">Europe</SelectItem>
                  <SelectItem value="AS">Asia</SelectItem>
                  <SelectItem value="GLOBAL">Global</SelectItem>
                </SelectContent>
              </Select>

              <div className="w-[150px]">
                <Input
                  type="number"
                  placeholder={t('dataSize') + ' (GB)'}
                  value={selectedDataSize}
                  onChange={(e) => setSelectedDataSize(e.target.value)}
                  min="0"
                />
              </div>

              <Button variant="outline" onClick={clearFilters}>
                {tCommon('clear')}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 套餐列表 */}
        <Card>
          <CardContent className="p-0">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                {tCommon('loading')}...
              </div>
            ) : filteredPackages.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {tCommon('noData')}
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Data</TableHead>
                    <TableHead>Duration (days)</TableHead>
                    <TableHead>Per GB</TableHead>
                    <TableHead>Sug Retail</TableHead>
                    <TableHead>Speed</TableHead>
                    <TableHead>Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPackages.map((pkg) => {
                    const perGB = pkg.dataVolume > 0 ? (pkg.price / (pkg.dataVolume / (1024 * 1024 * 1024))).toFixed(0) : '-'
                    const sugRetail = (pkg.price * 1.2).toFixed(0)

                    return (
                      <TableRow key={pkg.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <img
                              src={`https://flagcdn.com/w20/${pkg.locationCodes[0]?.toLowerCase()}.png`}
                              alt=""
                              className="w-5 h-4 object-cover rounded-sm"
                              onError={(e) => {
                                e.currentTarget.style.display = 'none'
                              }}
                            />
                            <span className="font-medium">{pkg.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>{pkg.currency}{pkg.price}</TableCell>
                        <TableCell>{formatDataVolume(pkg.dataVolume)}</TableCell>
                        <TableCell>{pkg.validityDays}</TableCell>
                        <TableCell>{pkg.currency}{perGB}</TableCell>
                        <TableCell>{pkg.currency}{sugRetail}</TableCell>
                        <TableCell>
                          <span className="text-sm text-muted-foreground">
                            {pkg.networkTypes.join('/')}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleViewDetails(pkg)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </PageGuard>
  )
}
