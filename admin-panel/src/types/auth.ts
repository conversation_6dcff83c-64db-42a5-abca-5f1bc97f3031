export type UserRole = 'USER' | 'RESELLER' | 'ENTERPRISE' | 'ADMIN'

export type UserStatus = 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'PENDING'

export interface User {
  id: string
  email: string
  name: string
  role: User<PERSON>ole
  mobile?: string
  status: UserStatus
  createdAt: string
  lastLoginAt?: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  id: string
  email: string
  name: string
  role: UserRole
  token: string
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  _initialized: boolean
}

export interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>
  logout: () => void
  setUser: (user: User) => void
  setToken: (token: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  initializeAuth: () => void
}

export type AuthStore = AuthState & AuthActions 