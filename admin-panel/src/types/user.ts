import { User, UserRole, UserStatus } from './auth'

// 用户查询参数
export interface UserQueryParams {
  page?: number
  pageSize?: number
  search?: string
  status?: UserStatus | 'ALL'
  sortBy?: 'name' | 'email' | 'createdAt' | 'lastLoginAt'
  sortOrder?: 'asc' | 'desc'
  dateFrom?: string
  dateTo?: string
}

// 用户列表响应
export interface UserListResponse {
  users: User[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 用户创建请求 (基于API文档)
export interface CreateUserRequest {
  email: string
  name: string
  mobile?: string
  password: string
}

// 用户更新请求 (基于API文档)
export interface UpdateUserRequest {
  name?: string
  mobile?: string
}

// 用户详情扩展信息 (基于API文档)
export interface UserDetails extends User {
  // 基本统计信息
  totalOrders?: number
  totalSpent?: number
  esimCount?: number
}

// 用户操作日志
export interface UserActivityLog {
  id: string
  userId: string
  action: string
  description: string
  ipAddress?: string
  userAgent?: string
  createdAt: string
  metadata?: Record<string, any>
}

// 批量操作请求
export interface BatchUserOperation {
  userIds: string[]
  operation: 'activate' | 'deactivate' | 'suspend' | 'delete' | 'changeRole'
  params?: {
    role?: UserRole
    status?: UserStatus
    reason?: string
  }
}

// 用户统计信息 (简化版，仅用于普通用户管理)
export interface UserStats {
  total: number
  active: number
  inactive: number
  suspended: number
  pending: number
}

// 用户导出选项
export interface UserExportOptions {
  format: 'csv' | 'excel'
  fields: string[]
  filters?: UserQueryParams
  includeStats?: boolean
}

// 用户表格列定义
export interface UserTableColumn {
  key: string
  label: string
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
  render?: (user: User) => React.ReactNode
}
