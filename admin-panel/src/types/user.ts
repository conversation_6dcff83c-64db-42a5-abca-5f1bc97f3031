import { User, UserRole, UserStatus } from './auth'

// 用户查询参数
export interface UserQueryParams {
  page?: number
  pageSize?: number
  search?: string
  role?: UserRole | 'ALL'
  status?: UserStatus | 'ALL'
  sortBy?: 'name' | 'email' | 'createdAt' | 'lastLoginAt'
  sortOrder?: 'asc' | 'desc'
  dateFrom?: string
  dateTo?: string
}

// 用户列表响应
export interface UserListResponse {
  users: User[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 用户创建请求
export interface CreateUserRequest {
  email: string
  name: string
  firstName?: string
  lastName?: string
  mobile?: string
  role: UserRole
  status?: UserStatus
  country?: string
  timezone?: string
  language?: string
  password?: string // 可选，如果不提供则发送邀请邮件
}

// 用户更新请求
export interface UpdateUserRequest {
  name?: string
  firstName?: string
  lastName?: string
  mobile?: string
  role?: UserRole
  status?: UserStatus
  country?: string
  timezone?: string
  language?: string
  avatar?: string
}

// 用户详情扩展信息
export interface UserDetails extends User {
  // 账户信息
  creditBalance?: number
  totalCreditEarned?: number
  totalCreditSpent?: number
  
  // 订单统计
  totalOrders: number
  completedOrders: number
  cancelledOrders: number
  totalSpent: number
  
  // eSIM统计
  esimCount: number
  activeEsims: number
  expiredEsims: number
  
  // 活动记录
  lastActivity?: string
  loginCount?: number
  
  // 关联信息
  parentReseller?: {
    id: string
    name: string
    email: string
  }
  
  // 企业信息（如果是企业用户）
  department?: string
  position?: string
  employeeId?: string
}

// 用户操作日志
export interface UserActivityLog {
  id: string
  userId: string
  action: string
  description: string
  ipAddress?: string
  userAgent?: string
  createdAt: string
  metadata?: Record<string, any>
}

// 批量操作请求
export interface BatchUserOperation {
  userIds: string[]
  operation: 'activate' | 'deactivate' | 'suspend' | 'delete' | 'changeRole'
  params?: {
    role?: UserRole
    status?: UserStatus
    reason?: string
  }
}

// 用户统计信息
export interface UserStats {
  total: number
  active: number
  inactive: number
  suspended: number
  pending: number
  byRole: {
    USER: number
    RESELLER: number
    ENTERPRISE: number
    ADMIN: number
  }
  recentRegistrations: number // 最近30天注册数
  recentLogins: number // 最近30天登录数
}

// 用户导出选项
export interface UserExportOptions {
  format: 'csv' | 'excel'
  fields: string[]
  filters?: UserQueryParams
  includeStats?: boolean
}

// 用户表格列定义
export interface UserTableColumn {
  key: string
  label: string
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
  render?: (user: User) => React.ReactNode
}
